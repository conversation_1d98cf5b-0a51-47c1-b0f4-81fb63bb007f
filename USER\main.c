#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include "../HARDWARE/max262/MAX262.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 优化内存使用
#define ADC1_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 优化内存使用
#define ADC2_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1幅度（滤波器输入）
    float adc2_amplitude;   // ADC2幅度（滤波器输出）
    float magnitude_db;     // 幅度响应(dB)
    float phase_deg;        // 相位响应(度)
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_BUFFER_SIZE 50  // 只保存最近50个点的结果用于分析
#define SMOOTH_FILTER_SIZE 5  // 平滑滤波器窗口大小
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 减少内存使用：50×20字节=1KB
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=低频检测，1=完整扫频，2=归一化输出

// 平滑滤波器相关变量
float smooth_buffer_phase2[SMOOTH_FILTER_SIZE];  // 第二次扫频平滑缓冲区
float smooth_buffer_phase3[SMOOTH_FILTER_SIZE];  // 第三次扫频平滑缓冲区
uint8_t smooth_index_phase2 = 0;                 // 第二次扫频平滑缓冲区索引
uint8_t smooth_index_phase3 = 0;                 // 第三次扫频平滑缓冲区索引
uint8_t smooth_count_phase2 = 0;                 // 第二次扫频平滑缓冲区有效数据计数
uint8_t smooth_count_phase3 = 0;                 // 第三次扫频平滑缓冲区有效数据计数

// 滤波器类型判断相关变量
float freq_1kHz_ratio = 0.0f;             // 1kHz频率点的归一化电压幅度比
float freq_1_2kHz_ratio = 0.0f;           // 1.2kHz频率点的归一化电压幅度比
float freq_399_8kHz_ratio = 0.0f;         // 399.8kHz频率点的归一化电压幅度比
float freq_400kHz_ratio = 0.0f;           // 400kHz频率点的归一化电压幅度比

// 第一次扫频的低频检测结果
float first_sweep_1kHz_ratio = 0.0f;      // 第一次扫频1kHz的电压幅度比
float first_sweep_1_2kHz_ratio = 0.0f;    // 第一次扫频1.2kHz的电压幅度比
uint8_t amplitude_multiplier = 1;         // 电压幅度比倍数（1或2）
uint8_t low_freq_points_completed = 0;    // 低频点完成计数

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void AnalyzeFilterCharacteristics(void);
void DetermineFilterType(void);

// 传递函数和参数计算函数声明
void IdentifyCircuitModel(void);
void CalculateFilterParameters(void);
void DisplayTransferFunction(void);
void DisplayFilterParameters(float K, float omega_0, float Q, const char* filter_type);
float FindCutoffFrequency(void);
float FindLowPassCutoff(void);
float FindHighPassCutoff(void);
float CalculateQFactor(float f0, float f1, float f2);
float CalculateBandPassQ(float center_freq);
float CalculateBandStopQ(float notch_freq);
float CalculateLowPassQ(float cutoff_freq);
float CalculateHighPassQ(float cutoff_freq);
float FindFreqAbove(float start_freq, float target_amplitude);
float FindFreqBelow(float start_freq, float target_amplitude);

// MAX262滤波器配置函数声明
void ConfigureMAX262Filter(const char* filter_type, float f0, float Q);

// 平滑滤波函数声明
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size);
void InitSmoothFilters(void);

// 扫频测试函数声明（简化版）

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义七个按钮 - 更大尺寸便于操作
Button_t buttons[7] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY}      // 扫频测试按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 7; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 7; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    // 初始化MAX262的MCO时钟输出
    MAX262_MCO_Init();

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 7;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                // 根据扫频阶段决定下一个频率点
                uint8_t continue_sweep = 0;

                if (sweep_phase == 0) {
                    // 第一阶段：只扫1kHz和1.2kHz
                    printf("DEBUG Phase 0 frequency control: low_freq_points_completed=%d\r\n", low_freq_points_completed);

                    if (low_freq_points_completed < 2) {
                        float next_freq;
                        if (low_freq_points_completed == 1) {
                            next_freq = 1200.0f;  // 第二个点是1.2kHz
                            AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);
                            printf("DEBUG Phase 0: Set frequency to %.1f Hz\r\n", next_freq);

                            // 显示进度
                            char progress_info[100];
                            sprintf(progress_info, "Phase 1: Low Freq Test (%.1fkHz)", next_freq/1000.0f);
                            lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                            continue_sweep = 1;
                        } else {
                            printf("DEBUG Phase 0: Waiting for first point to complete\r\n");
                        }
                    } else {
                        printf("DEBUG Phase 0: Both low freq points completed\r\n");
                    }
                } else if (sweep_phase == 1) {
                    // 第二阶段：完整扫频
                    if (current_sweep_point < SWEEP_POINTS) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                        // 显示进度
                        char progress_info[100];
                        sprintf(progress_info, "Phase 2: Full Sweep %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                        lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                        continue_sweep = 1;
                    }
                } else if (sweep_phase == 2) {
                    // 第三阶段：归一化处理
                    if (current_sweep_point < SWEEP_POINTS) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                        // 显示进度
                        char progress_info[100];
                        sprintf(progress_info, "Phase 3: Normalize %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                        lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                        continue_sweep = 1;
                    }
                }

                if (continue_sweep) {
                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，判断倍数并开始第二阶段
                        if (first_sweep_1kHz_ratio > 0.0f && first_sweep_1_2kHz_ratio > 0.0f) {
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                            if (first_sweep_1kHz_ratio < 0.5f && first_sweep_1_2kHz_ratio < 0.5f) {
                                amplitude_multiplier = 2;
                                printf("Low frequency ratios detected, applying 2x multiplier\r\n");
                            } else {
                                amplitude_multiplier = 1;
                                printf("Normal frequency ratios, using 1x multiplier\r\n");
                            }
                        } else {
                            amplitude_multiplier = 1;
                            printf("Insufficient low frequency data, using 1x multiplier\r\n");
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                        }
                        printf("DEBUG: Final amplitude_multiplier = %d\r\n", amplitude_multiplier);

                        sweep_phase = 1;
                        current_sweep_point = 0;
                        max_voltage_ratio = 0.0f;  // 重置最大值

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: Full Sweep", BLUE);
                    } else if (sweep_phase == 1) {
                        // 第二阶段完成，开始第三阶段
                        sweep_phase = 2;
                        current_sweep_point = 0;

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 3: Normalize", BLUE);
                    } else {
                        // 第三阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        // 判断并显示滤波器类型
                        DetermineFilterType();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Complete!", GREEN);
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：低频检测

    // 初始化滤波器类型判断相关变量
    freq_1kHz_ratio = 0.0f;
    freq_1_2kHz_ratio = 0.0f;
    freq_399_8kHz_ratio = 0.0f;
    freq_400kHz_ratio = 0.0f;

    // 初始化第一次扫频的低频检测变量
    first_sweep_1kHz_ratio = 0.0f;
    first_sweep_1_2kHz_ratio = 0.0f;
    amplitude_multiplier = 1;
    low_freq_points_completed = 0;

    // 初始化平滑滤波器
    InitSmoothFilters();

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
    printf("DEBUG StartSweepTest: Set initial frequency to %.1f Hz\r\n", start_freq);

    // 开始扫频测试，无串口输出

    // 等待频率稳定
    delay_ms(10);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 调试信息：显示当前状态
    if (current_sweep_point < 3) {
        printf("DEBUG ProcessSweepPoint: phase=%d, point=%d, freq=%.1f, multiplier=%d\r\n",
               sweep_phase, current_sweep_point, current_freq, amplitude_multiplier);
    }

    // 计算ADC1和ADC2的RMS值（去除直流分量）
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 计算直流分量
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_dc += adc1_sample_buffer[i];
        adc2_dc += adc2_sample_buffer[i];
    }
    adc1_dc /= ADC1_SAMPLE_SIZE;
    adc2_dc /= ADC2_SAMPLE_SIZE;

    // 计算RMS值（去除直流分量）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_ac = adc1_sample_buffer[i] - adc1_dc;
        float adc2_ac = adc2_sample_buffer[i] - adc2_dc;
        adc1_rms += adc1_ac * adc1_ac;
        adc2_rms += adc2_ac * adc2_ac;
    }
    adc1_rms = sqrtf(adc1_rms / ADC1_SAMPLE_SIZE);
    adc2_rms = sqrtf(adc2_rms / ADC2_SAMPLE_SIZE);

    // 计算幅度响应 (dB) - 输出/输入
    float magnitude_db;
    if (adc2_rms > 0 && adc1_rms > 0) {
        magnitude_db = 20.0f * log10f(adc2_rms / adc1_rms);
    } else {
        magnitude_db = -100.0f; // 很小的值
    }

    // 简化的相位计算（基于峰值位置差）
    int adc1_peak_idx = 0, adc2_peak_idx = 0;
    float adc1_max = 0, adc2_max = 0;

    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_val = fabsf(adc1_sample_buffer[i] - adc1_dc);
        float adc2_val = fabsf(adc2_sample_buffer[i] - adc2_dc);

        if (adc1_val > adc1_max) {
            adc1_max = adc1_val;
            adc1_peak_idx = i;
        }
        if (adc2_val > adc2_max) {
            adc2_max = adc2_val;
            adc2_peak_idx = i;
        }
    }

    // 计算相位差（度）
    float sample_period = 1.0f / 815534.0f; // 采样周期
    float signal_period = 1.0f / current_freq; // 信号周期
    float time_diff = (adc1_peak_idx - adc2_peak_idx) * sample_period;
    float phase_deg = (time_diff / signal_period) * 360.0f;

    // 限制相位范围到 -180 到 +180 度
    while (phase_deg > 180.0f) {
        phase_deg -= 360.0f;
    }
    while (phase_deg < -180.0f) {
        phase_deg += 360.0f;
    }

    // 计算电压幅度比（滤波器输出/输入，线性比值，不是dB）
    float voltage_ratio = 0.0f;
    if (adc1_rms > 0) {
        voltage_ratio = adc2_rms / adc1_rms;
    }

    if (sweep_phase == 0) {
        // 第一阶段：只检测1kHz和1.2kHz
        printf("DEBUG Phase 0: current_freq=%.1f, checking for 1kHz and 1.2kHz\r\n", current_freq);

        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            first_sweep_1kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1kHz ratio: %.6f (completed count: %d)\r\n", voltage_ratio, low_freq_points_completed);
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            first_sweep_1_2kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1.2kHz ratio: %.6f (completed count: %d)\r\n", voltage_ratio, low_freq_points_completed);
        } else {
            printf("DEBUG Phase 0: Frequency %.1f not matched for low freq detection\r\n", current_freq);
        }
    } else if (sweep_phase == 1) {
        // 第二阶段：完整扫频，应用倍数和平滑滤波，输出到串口
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        // 应用平滑滤波
        float smoothed_ratio = ApplySmoothFilter(adjusted_voltage_ratio,
                                               smooth_buffer_phase2,
                                               &smooth_index_phase2,
                                               &smooth_count_phase2,
                                               SMOOTH_FILTER_SIZE);

        printf("%.1f\t%.6f\r\n", current_freq, smoothed_ratio);

        // 调试信息：显示前几个点的倍数应用情况
        if (current_sweep_point < 5) {
            printf("DEBUG: Point %d, original=%.6f, multiplier=%d, adjusted=%.6f, smoothed=%.6f\r\n",
                   current_sweep_point, voltage_ratio, amplitude_multiplier, adjusted_voltage_ratio, smoothed_ratio);
        }

        // 寻找最大值（使用平滑后的值）
        if (smoothed_ratio > max_voltage_ratio) {
            max_voltage_ratio = smoothed_ratio;
        }
    } else {
        // 第三阶段：归一化处理，应用平滑滤波，不输出串口数据
        float normalized_ratio = 0.0f;
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        if (max_voltage_ratio > 0.0f) {
            normalized_ratio = adjusted_voltage_ratio / max_voltage_ratio;
        }

        // 应用平滑滤波到归一化幅度比
        float smoothed_normalized_ratio = ApplySmoothFilter(normalized_ratio,
                                                          smooth_buffer_phase3,
                                                          &smooth_index_phase3,
                                                          &smooth_count_phase3,
                                                          SMOOTH_FILTER_SIZE);

        // 存储特定频率点的平滑后归一化电压幅度比用于滤波器类型判断
        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            freq_1kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            freq_1_2kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 399800.0f) < 1.0f) { // 399.8kHz ± 1Hz
            freq_399_8kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 400000.0f) < 1.0f) { // 400kHz ± 1Hz
            freq_400kHz_ratio = smoothed_normalized_ratio;
        }
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_rms;
    sweep_results[buffer_idx].adc2_amplitude = adc2_rms;
    sweep_results[buffer_idx].magnitude_db = magnitude_db;
    sweep_results[buffer_idx].phase_deg = phase_deg;

    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}

void AnalyzeFilterCharacteristics(void)
{
    printf("=== FILTER ANALYSIS (Based on Recent Data) ===\r\n");

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        printf("Insufficient data for analysis\r\n");
        printf("=== END OF ANALYSIS ===\r\n");
        return;
    }

    // 找到最大和最小幅度
    float max_magnitude = -100.0f;
    float min_magnitude = 100.0f;
    float max_freq = 0, min_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
            if (sweep_results[i].magnitude_db < min_magnitude) {
                min_magnitude = sweep_results[i].magnitude_db;
                min_freq = sweep_results[i].frequency;
            }
        }
    }

    printf("Recent Data Analysis:\r\n");
    printf("Max Gain: %.2f dB at %.1f Hz\r\n", max_magnitude, max_freq);
    printf("Min Gain: %.2f dB at %.1f Hz\r\n", min_magnitude, min_freq);
    printf("Gain Range: %.2f dB\r\n", max_magnitude - min_magnitude);

    // 简单的滤波器类型判断
    float gain_variation = max_magnitude - min_magnitude;
    if (gain_variation > 10.0f) {
        printf("Filter Type: Significant frequency response variation detected\r\n");
    } else if (max_magnitude > -1.0f) {
        printf("Filter Type: Likely passband region\r\n");
    } else if (max_magnitude < -10.0f) {
        printf("Filter Type: Likely stopband region\r\n");
    } else {
        printf("Filter Type: Transition region\r\n");
    }

    printf("Note: Complete analysis requires full sweep data\r\n");
    printf("=== END OF ANALYSIS ===\r\n");
}

void DetermineFilterType(void)
{
    // 判断滤波器类型的标准：
    // 1、低通滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.5且399.8kHz和400kHz的归一化电压幅度比均小于0.5
    // 2、高通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.5且399.8kHz和400kHz的归一化电压幅度比均大于0.5
    // 3、带通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.5且399.8kHz和400kHz的归一化电压幅度比均小于0.5
    // 4、带阻滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.5且399.8kHz和400kHz的归一化电压幅度比均大于0.5

    char filter_type[50] = "Unknown";
    uint16_t display_color = RED;

    // 检查是否有足够的数据进行判断
//    if (freq_1kHz_ratio == 0.0f || freq_1_2kHz_ratio == 0.0f ||
//        freq_399_8kHz_ratio == 0.0f || freq_400kHz_ratio == 0.0f) {
//        sprintf(filter_type, "Insufficient Data");
//        display_color = GRAY;
//    } else 
{
        // 判断低频段（1kHz和1.2kHz）
        uint8_t low_freq_high = (freq_1kHz_ratio > 0.5f) && (freq_1_2kHz_ratio > 0.5f);

        // 判断高频段（399.8kHz和400kHz）
        uint8_t high_freq_high = (freq_399_8kHz_ratio > 0.5f) && (freq_400kHz_ratio > 0.5f);

        if (low_freq_high && !high_freq_high) {
            // 低频高，高频低 -> 低通滤波器
            sprintf(filter_type, "Low-Pass Filter");
            display_color = GREEN;
        } else if (!low_freq_high && high_freq_high) {
            // 低频低，高频高 -> 高通滤波器
            sprintf(filter_type, "High-Pass Filter");
            display_color = BLUE;
        } else if (!low_freq_high && !high_freq_high) {
            // 低频低，高频低 -> 带通滤波器
            sprintf(filter_type, "Band-Pass Filter");
            display_color = MAGENTA;
        } else if (low_freq_high && high_freq_high) {
            // 低频高，高频高 -> 带阻滤波器
            sprintf(filter_type, "Band-Stop Filter");
            display_color = YELLOW;
        }
    }

    // 在LCD屏幕上显示滤波器类型
    // 清除之前的显示区域
    lcd_fill(0, 110, lcddev.width, 150, WHITE);

    // 显示滤波器类型
    lcd_show_string(10, 110, lcddev.width, 20, 16, "Filter Type:", BLACK);
    lcd_show_string(10, 130, lcddev.width, 20, 16, filter_type, display_color);

    // 显示具体的测量值（调试信息）
    char debug_info[100];
    sprintf(debug_info, "1kHz:%.3f 1.2k:%.3f", freq_1kHz_ratio, freq_1_2kHz_ratio);
    lcd_show_string(10, 150, lcddev.width, 20, 12, debug_info, BLACK);

    sprintf(debug_info, "399.8k:%.3f 400k:%.3f", freq_399_8kHz_ratio, freq_400kHz_ratio);
    lcd_show_string(10, 170, lcddev.width, 20, 12, debug_info, BLACK);

    // 通过串口输出结果
    printf("=== FILTER TYPE DETERMINATION ===\r\n");
    printf("Filter Type: %s\r\n", filter_type);
    printf("Amplitude Multiplier Used: %dx\r\n", amplitude_multiplier);
    printf("First Sweep Low Freq Data:\r\n");
    printf("  1kHz (raw): %.6f\r\n", first_sweep_1kHz_ratio);
    printf("  1.2kHz (raw): %.6f\r\n", first_sweep_1_2kHz_ratio);
    printf("Final Normalized Data:\r\n");
    printf("  1kHz: %.6f\r\n", freq_1kHz_ratio);
    printf("  1.2kHz: %.6f\r\n", freq_1_2kHz_ratio);
    printf("  399.8kHz: %.6f\r\n", freq_399_8kHz_ratio);
    printf("  400kHz: %.6f\r\n", freq_400kHz_ratio);
    printf("=== END OF DETERMINATION ===\r\n");

    // 在滤波器类型确定后，计算传递函数参数并显示
    IdentifyCircuitModel();
}

/**
 * @brief  初始化平滑滤波器
 * @param  None
 * @retval None
 * @note   清空所有平滑滤波器的缓冲区和计数器
 */
void InitSmoothFilters(void)
{
    // 清空第二次扫频平滑缓冲区
    for (int i = 0; i < SMOOTH_FILTER_SIZE; i++) {
        smooth_buffer_phase2[i] = 0.0f;
        smooth_buffer_phase3[i] = 0.0f;
    }

    // 重置索引和计数器
    smooth_index_phase2 = 0;
    smooth_index_phase3 = 0;
    smooth_count_phase2 = 0;
    smooth_count_phase3 = 0;
}

/**
 * @brief  应用移动平均平滑滤波器
 * @param  new_value: 新的输入值
 * @param  buffer: 滤波器缓冲区指针
 * @param  index: 缓冲区索引指针
 * @param  count: 有效数据计数指针
 * @param  buffer_size: 缓冲区大小
 * @retval 平滑后的输出值
 * @note   使用移动平均算法对输入数据进行平滑处理
 */
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size)
{
    // 将新值存入缓冲区
    buffer[*index] = new_value;

    // 更新索引（循环缓冲区）
    *index = (*index + 1) % buffer_size;

    // 更新有效数据计数（最多为缓冲区大小）
    if (*count < buffer_size) {
        (*count)++;
    }

    // 计算移动平均值
    float sum = 0.0f;
    for (int i = 0; i < *count; i++) {
        sum += buffer[i];
    }

    return sum / (*count);
}

/**
 * @brief  识别电路模型并计算传递函数参数
 * @param  None
 * @retval None
 * @note   根据滤波器类型和扫频结果计算传递函数参数
 */
void IdentifyCircuitModel(void)
{
    printf("=== CIRCUIT MODEL IDENTIFICATION ===\r\n");

    // 检查是否有足够的数据
    if (max_voltage_ratio <= 0.0f) {
        printf("Error: No valid sweep data available\r\n");
        return;
    }

    // 计算滤波器参数
    CalculateFilterParameters();

    // 显示传递函数
    DisplayTransferFunction();

    printf("=== END OF IDENTIFICATION ===\r\n");
}

/**
 * @brief  计算二阶RLC滤波器参数
 * @param  None
 * @retval None
 * @note   基于第二次扫频的未归一化结果计算K、ω₀、Q参数
 */
void CalculateFilterParameters(void)
{
    // 通带增益K（使用第二次扫频的最大值，未归一化）
    float K = max_voltage_ratio;

    // 初始化参数
    float omega_0 = 2.0f * 3.14159f * 10000.0f; // 默认值
    float Q = 0.707f; // 默认品质因数
    float f_0 = 10000.0f; // 默认频率

    // 首先根据四个特定频率点判断滤波器类型
    printf("=== FILTER TYPE IDENTIFICATION ===\r\n");
    printf("Frequency response analysis (voltage amplitude ratios):\r\n");
    printf("  1.0 kHz: %.3f %s\r\n", freq_1kHz_ratio, (freq_1kHz_ratio < 0.5f) ? "(<0.5)" : "(>=0.5)");
    printf("  1.2 kHz: %.3f %s\r\n", freq_1_2kHz_ratio, (freq_1_2kHz_ratio < 0.5f) ? "(<0.5)" : "(>=0.5)");
    printf("399.8 kHz: %.3f %s\r\n", freq_399_8kHz_ratio, (freq_399_8kHz_ratio < 0.5f) ? "(<0.5)" : "(>=0.5)");
    printf("400.0 kHz: %.3f %s\r\n", freq_400kHz_ratio, (freq_400kHz_ratio < 0.5f) ? "(<0.5)" : "(>=0.5)");

    // 根据判断逻辑确定滤波器类型并计算参数
    if (freq_1kHz_ratio > 0.5f && freq_1_2kHz_ratio > 0.5f &&
        freq_399_8kHz_ratio < 0.5f && freq_400kHz_ratio < 0.5f) {
        printf("Identified Filter Type: LOW-PASS (low freq > 0.5, high freq < 0.5)\r\n");
        printf("=== CALCULATING LOW-PASS PARAMETERS ===\r\n");

        // 使用实际扫频数据寻找低通截止频率（-3dB点）
        f_0 = FindLowPassCutoff();

        printf("Low-Pass Filter: Found cutoff frequency = %.1f Hz using voltage amplitude method\r\n", f_0);

        // 验证截止频率的合理性
        if (f_0 < 100.0f || f_0 > 500000.0f) {
            printf("Warning: Cutoff frequency %.1f Hz may be outside reasonable range\r\n", f_0);
            // 使用默认值，但仍然基于扫频数据
            f_0 = 10000.0f; // 默认10kHz
            printf("Using default cutoff frequency: %.1f Hz\r\n", f_0);
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 基于过渡带宽度计算Q值（-3dB到-20dB）
        Q = CalculateLowPassQ(f_0);

        DisplayFilterParameters(K, omega_0, Q, "Low-Pass");

        // 配置MAX262为低通滤波器 - 传递截止频率
        printf("Low-Pass Filter: Cutoff Frequency = %.1f Hz\r\n", f_0);
        ConfigureMAX262Filter("Low-Pass", f_0, Q);

    } else if (freq_1kHz_ratio < 0.5f && freq_1_2kHz_ratio < 0.5f &&
               freq_399_8kHz_ratio > 0.5f && freq_400kHz_ratio > 0.5f) {
        printf("Identified Filter Type: HIGH-PASS (low freq < 0.5, high freq > 0.5)\r\n");
        printf("=== CALCULATING HIGH-PASS PARAMETERS ===\r\n");

        // 使用实际扫频数据寻找高通截止频率（-3dB点）
        f_0 = FindHighPassCutoff();

        printf("High-Pass Filter: Found cutoff frequency = %.1f Hz using voltage amplitude method\r\n", f_0);

        // 验证截止频率的合理性
        if (f_0 < 100.0f || f_0 > 200000.0f) {
            printf("Warning: Cutoff frequency %.1f Hz may be outside reasonable range\r\n", f_0);
            // 使用默认值，但仍然基于扫频数据
            f_0 = 5000.0f; // 默认5kHz
            printf("Using default cutoff frequency: %.1f Hz\r\n", f_0);
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 基于过渡带宽度计算Q值（-20dB到-3dB）
        Q = CalculateHighPassQ(f_0);

        DisplayFilterParameters(K, omega_0, Q, "High-Pass");

        // 配置MAX262为高通滤波器 - 传递截止频率
        printf("High-Pass Filter: Cutoff Frequency = %.1f Hz\r\n", f_0);
        ConfigureMAX262Filter("High-Pass", f_0, Q);

    } else if (freq_1kHz_ratio < 0.5f && freq_1_2kHz_ratio < 0.5f &&
               freq_399_8kHz_ratio < 0.5f && freq_400kHz_ratio < 0.5f) {
        printf("Identified Filter Type: BAND-PASS (low and high freq < 0.5)\r\n");
        printf("=== CALCULATING BAND-PASS PARAMETERS ===\r\n");

        // 对于带通滤波器，寻找最大增益点作为中心频率
        if (total_sweep_points > 10) {
            float max_ratio = 0.0f;
            float center_freq = 50000.0f;
            int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

            // 寻找最大增益点
            for (int i = 0; i < valid_points; i++) {
                if (sweep_results[i].adc2_amplitude > max_ratio) {
                    max_ratio = sweep_results[i].adc2_amplitude;
                    center_freq = sweep_results[i].frequency;
                }
            }
            f_0 = center_freq;
            printf("Found maximum gain at %.1f Hz (amplitude: %.3f)\r\n", f_0, max_ratio);
        } else {
            f_0 = 50000.0f; // 默认50kHz
            printf("Using default center frequency: %.1f Hz\r\n", f_0);
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 品质因数Q的计算：基于第二次扫频数据的-3dB带宽
        // 对于带通滤波器，Q = f0/BW
        Q = CalculateBandPassQ(f_0);

        DisplayFilterParameters(K, omega_0, Q, "Band-Pass");

        // 配置MAX262为带通滤波器 - 传递中心频率
        printf("Band-Pass Filter: Center Frequency = %.1f Hz\r\n", f_0);
        ConfigureMAX262Filter("Band-Pass", f_0, Q);

    } else if (freq_1kHz_ratio > 0.5f && freq_1_2kHz_ratio > 0.5f &&
               freq_399_8kHz_ratio > 0.5f && freq_400kHz_ratio > 0.5f) {
        printf("Identified Filter Type: BAND-STOP (all frequencies > 0.5)\r\n");
        printf("=== CALCULATING BAND-STOP PARAMETERS ===\r\n");

        // 对于带阻滤波器，寻找最小增益点作为陷波频率
        if (total_sweep_points > 10) {
            float min_ratio = 1000.0f;
            float notch_freq = 50000.0f;
            int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

            // 寻找最小增益点
            for (int i = 0; i < valid_points; i++) {
                if (sweep_results[i].adc2_amplitude < min_ratio && sweep_results[i].adc2_amplitude > 0.0f) {
                    min_ratio = sweep_results[i].adc2_amplitude;
                    notch_freq = sweep_results[i].frequency;
                }
            }
            f_0 = notch_freq;
            printf("Found minimum gain at %.1f Hz (amplitude: %.3f)\r\n", f_0, min_ratio);
        } else {
            f_0 = 50000.0f; // 默认50kHz
            printf("Using default notch frequency: %.1f Hz\r\n", f_0);
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 带阻滤波器的Q值决定陷波的尖锐程度
        // 基于第二次扫频数据计算实际Q值
        Q = CalculateBandStopQ(f_0);

        DisplayFilterParameters(K, omega_0, Q, "Band-Stop");

        // 配置MAX262为带阻滤波器 - 传递中心频率（陷波频率）
        printf("Band-Stop Filter: Notch Frequency (Center) = %.1f Hz\r\n", f_0);
        ConfigureMAX262Filter("Band-Stop", f_0, Q);

    } else {
        // 未知类型，使用默认参数
        printf("Unknown filter type, using default parameters\r\n");
        DisplayFilterParameters(K, omega_0, Q, "Unknown");
    }
}

/**
 * @brief  显示传递函数
 * @param  None
 * @retval None
 * @note   根据滤波器类型在LCD上显示相应的传递函数
 */
void DisplayTransferFunction(void)
{
    // 清除传递函数显示区域
    lcd_fill(0, 190, lcddev.width, 270, WHITE);

    // 显示传递函数标题
    lcd_show_string(10, 190, lcddev.width, 20, 14, "Transfer Function:", BLACK);

    // 根据滤波器类型显示相应的传递函数
    if (freq_1kHz_ratio > 0.5f && freq_1_2kHz_ratio > 0.5f &&
        freq_399_8kHz_ratio < 0.5f && freq_400kHz_ratio < 0.5f) {
        // 低通滤波器: H(s) = K * ω₀² / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * w0^2", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio < 0.5f && freq_1_2kHz_ratio < 0.5f &&
               freq_399_8kHz_ratio > 0.5f && freq_400kHz_ratio > 0.5f) {
        // 高通滤波器: H(s) = K * s² / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * s^2", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio < 0.5f && freq_1_2kHz_ratio < 0.5f &&
               freq_399_8kHz_ratio < 0.5f && freq_400kHz_ratio < 0.5f) {
        // 带通滤波器: H(s) = K * ω₀/Q*s / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * w0/Q*s", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio > 0.5f && freq_1_2kHz_ratio > 0.5f &&
               freq_399_8kHz_ratio > 0.5f && freq_400kHz_ratio > 0.5f) {
        // 带阻滤波器: H(s) = K * (s² + ω₀²) / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K*(s^2+w0^2)", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else {
        // 未知类型
        lcd_show_string(10, 210, lcddev.width, 20, 12, "Unknown Filter Type", RED);
    }
}

/**
 * @brief  显示滤波器参数
 * @param  K: 通带增益
 * @param  omega_0: 中心/截止角频率 (rad/s)
 * @param  Q: 品质因数
 * @param  filter_type: 滤波器类型字符串
 * @retval None
 * @note   在LCD和串口上显示计算得到的滤波器参数
 */
void DisplayFilterParameters(float K, float omega_0, float Q, const char* filter_type)
{
    char param_buffer[100];

    // 计算频率 (Hz)
    float f_0 = omega_0 / (2.0f * 3.14159f);

    // 在LCD上显示参数（在传递函数下方）
    lcd_show_string(10, 255, lcddev.width, 20, 14, "Parameters:", BLACK);

    // 显示K值
    sprintf(param_buffer, "K = %.3f", K);
    lcd_show_string(10, 275, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示ω₀值
    sprintf(param_buffer, "w0 = %.1f rad/s", omega_0);
    lcd_show_string(10, 290, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示f₀值
    sprintf(param_buffer, "f0 = %.1f Hz", f_0);
    lcd_show_string(10, 305, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示Q值
    sprintf(param_buffer, "Q = %.3f", Q);
    lcd_show_string(10, 320, lcddev.width, 20, 12, param_buffer, GREEN);

    // 通过串口输出详细参数信息
    printf("=== FILTER PARAMETERS (基于第二次扫频未归一化结果) ===\r\n");
    printf("Filter Type: %s\r\n", filter_type);
    printf("通带增益 K = %.6f (由IdentifyCircuitModel函数测得)\r\n", K);
    printf("中心/截止角频率 omega_0 = %.2f rad/s\r\n", omega_0);
    printf("中心/截止频率 f_0 = %.2f Hz (其中 f_0 是从幅频曲线上测量出的频率)\r\n", f_0);
    printf("品质因数 Q = %.6f\r\n", Q);

    // 根据滤波器类型显示相应的传递函数参数说明
    if (strstr(filter_type, "Low-Pass") != NULL) {
        printf("低通滤波器传递函数: H(s) = K * omega_0^2 / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s\r\n", omega_0);
        printf("      Q = %.3f (巴特沃斯响应)\r\n", Q);
    } else if (strstr(filter_type, "High-Pass") != NULL) {
        printf("高通滤波器传递函数: H(s) = K * s^2 / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s\r\n", omega_0);
        printf("      Q = %.3f (巴特沃斯响应)\r\n", Q);
    } else if (strstr(filter_type, "Band-Pass") != NULL) {
        printf("带通滤波器传递函数: H(s) = K * (omega_0/Q)*s / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s (中心频率)\r\n", omega_0);
        printf("      Q = %.3f (品质因数，Q越高带宽越窄)\r\n", Q);
    } else if (strstr(filter_type, "Band-Stop") != NULL) {
        printf("带阻滤波器传递函数: H(s) = K * (s^2 + omega_0^2) / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s (陷波频率)\r\n", omega_0);
        printf("      Q = %.3f (品质因数，Q越高陷波越尖锐)\r\n", Q);
    }

    printf("说明: 这些参数采用第二次扫频的结果计算，即未归一化的结果\r\n");
    printf("=== END OF PARAMETERS ===\r\n");
}

/**
 * @brief  从扫频数据中寻找截止频率
 * @param  None
 * @retval 截止频率 (Hz)
 * @note   寻找-3dB点（约0.707倍最大值）
 */
float FindCutoffFrequency(void)
{
    if (total_sweep_points < 10) {
        return 10000.0f; // 默认值
    }

    // 寻找最大值
    float max_ratio = 0.0f;
    int max_index = 0;
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].adc2_amplitude > max_ratio) {
            max_ratio = sweep_results[i].adc2_amplitude;
            max_index = i;
        }
    }

    // 计算-3dB点的目标值
    float target_ratio = max_ratio * 0.707f;

    // 寻找最接近-3dB点的频率
    float cutoff_freq = 10000.0f;
    float min_diff = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        float diff = fabsf(sweep_results[i].adc2_amplitude - target_ratio);
        if (diff < min_diff) {
            min_diff = diff;
            cutoff_freq = sweep_results[i].frequency;
        }
    }

    return cutoff_freq;
}

/**
 * @brief  计算品质因数Q
 * @param  f0: 中心频率 (Hz)
 * @param  f1: 下-3dB频率 (Hz)
 * @param  f2: 上-3dB频率 (Hz)
 * @retval 品质因数Q
 * @note   Q = f0 / (f2 - f1)，其中f2-f1是-3dB带宽
 */
float CalculateQFactor(float f0, float f1, float f2)
{
    if (f2 <= f1 || f0 <= 0) {
        return 0.707f; // 默认值
    }

    float bandwidth = f2 - f1;
    float Q = f0 / bandwidth;

    // 限制Q值在合理范围内
    if (Q < 0.1f) Q = 0.1f;
    if (Q > 100.0f) Q = 100.0f;

    return Q;
}

/**
 * @brief  计算带通滤波器的品质因数Q
 * @param  center_freq: 中心频率 (Hz)
 * @retval 品质因数Q
 * @note   基于第二次扫频数据，寻找-3dB带宽计算Q = f0/BW
 */
float CalculateBandPassQ(float center_freq)
{
    if (total_sweep_points < 10) {
        return 5.0f; // 默认值
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    // 寻找中心频率处的最大幅度
    float max_amplitude = 0.0f;
    int center_index = -1;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].adc2_amplitude > max_amplitude) {
            max_amplitude = sweep_results[i].adc2_amplitude;
            center_index = i;
        }
    }

    if (center_index == -1 || max_amplitude <= 0.0f) {
        printf("CalculateBandPassQ: No valid center frequency found\r\n");
        return 5.0f;
    }

    // 计算-3dB点的目标幅度（0.707倍最大值）
    float target_amplitude = max_amplitude * 0.707f;

    // 寻找下-3dB频率（中心频率左侧）
    float f1 = center_freq;
    float min_diff_low = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].frequency < center_freq) {
            float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (diff < min_diff_low) {
                min_diff_low = diff;
                f1 = sweep_results[i].frequency;
            }
        }
    }

    // 寻找上-3dB频率（中心频率右侧）
    float f2 = center_freq;
    float min_diff_high = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].frequency > center_freq) {
            float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (diff < min_diff_high) {
                min_diff_high = diff;
                f2 = sweep_results[i].frequency;
            }
        }
    }

    // 计算Q值
    float bandwidth = f2 - f1;
    float Q = 5.0f; // 默认值

    if (bandwidth > 0 && center_freq > 0) {
        Q = center_freq / bandwidth;

        // 限制Q值在合理范围内
        if (Q < 0.5f) Q = 0.5f;
        if (Q > 50.0f) Q = 50.0f;

        printf("BandPass Q calculation: f0=%.1f, f1=%.1f, f2=%.1f, BW=%.1f, Q=%.3f\r\n",
               center_freq, f1, f2, bandwidth, Q);
    } else {
        printf("BandPass Q calculation failed: invalid bandwidth or center frequency\r\n");
    }

    return Q;
}

/**
 * @brief  计算带阻滤波器的品质因数Q
 * @param  notch_freq: 陷波频率 (Hz)
 * @retval 品质因数Q
 * @note   基于第二次扫频数据，寻找陷波深度和带宽计算Q
 */
float CalculateBandStopQ(float notch_freq)
{
    if (total_sweep_points < 10) {
        return 10.0f; // 默认值
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    // 寻找陷波频率处的最小幅度
    float min_amplitude = 1000.0f;
    int notch_index = -1;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].adc2_amplitude < min_amplitude) {
            min_amplitude = sweep_results[i].adc2_amplitude;
            notch_index = i;
        }
    }

    if (notch_index == -1) {
        printf("CalculateBandStopQ: No valid notch frequency found\r\n");
        return 10.0f;
    }

    // 寻找通带的平均幅度（远离陷波频率的点）
    float passband_sum = 0.0f;
    int passband_count = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float freq_diff = fabsf(sweep_results[i].frequency - notch_freq);
            // 选择距离陷波频率较远的点作为通带参考
            if (freq_diff > notch_freq * 0.2f) { // 距离陷波频率20%以上
                passband_sum += sweep_results[i].adc2_amplitude;
                passband_count++;
            }
        }
    }

    if (passband_count == 0) {
        printf("CalculateBandStopQ: No valid passband data found\r\n");
        return 10.0f;
    }

    float passband_avg = passband_sum / passband_count;

    // 计算-3dB点的目标幅度（通带平均值的0.707倍）
    float target_amplitude = passband_avg * 0.707f;

    // 寻找下-3dB频率（陷波频率左侧）
    float f1 = notch_freq;
    float min_diff_low = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].frequency < notch_freq) {
            float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (diff < min_diff_low) {
                min_diff_low = diff;
                f1 = sweep_results[i].frequency;
            }
        }
    }

    // 寻找上-3dB频率（陷波频率右侧）
    float f2 = notch_freq;
    float min_diff_high = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].frequency > notch_freq) {
            float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (diff < min_diff_high) {
                min_diff_high = diff;
                f2 = sweep_results[i].frequency;
            }
        }
    }

    // 计算Q值
    float bandwidth = f2 - f1;
    float Q = 10.0f; // 默认值

    if (bandwidth > 0 && notch_freq > 0) {
        Q = notch_freq / bandwidth;

        // 限制Q值在合理范围内
        if (Q < 1.0f) Q = 1.0f;
        if (Q > 100.0f) Q = 100.0f;

        printf("BandStop Q calculation: f0=%.1f, f1=%.1f, f2=%.1f, BW=%.1f, Q=%.3f\r\n",
               notch_freq, f1, f2, bandwidth, Q);
        printf("Passband avg=%.6f, notch min=%.6f, target=%.6f\r\n",
               passband_avg, min_amplitude, target_amplitude);
    } else {
        printf("BandStop Q calculation failed: invalid bandwidth or notch frequency\r\n");
    }

    return Q;
}

/**
 * @brief  根据扫频数据中的频率寻找对应的幅度值
 * @param  target_freq: 目标频率 (Hz)
 * @retval 对应的幅度值 (线性比值)，如果找不到返回0
 * @note   在扫频数据中寻找最接近目标频率的点
 */
float FindAmplitudeAtFreq(float target_freq)
{
    if (total_sweep_points < 5) {
        return 0.0f;
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    float closest_amplitude = 0.0f;
    float min_freq_diff = 1000000.0f;

    // 寻找最接近目标频率的数据点
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            float freq_diff = fabsf(sweep_results[i].frequency - target_freq);
            if (freq_diff < min_freq_diff) {
                min_freq_diff = freq_diff;
                closest_amplitude = sweep_results[i].adc2_amplitude;
            }
        }
    }

    return closest_amplitude;
}

/**
 * @brief  在指定频率之上寻找目标幅度对应的频率
 * @param  start_freq: 起始频率 (Hz)
 * @param  target_amplitude: 目标幅度值 (线性比值)
 * @retval 对应的频率 (Hz)，如果找不到返回start_freq
 * @note   用于寻找低通滤波器的衰减点
 */
float FindFreqAbove(float start_freq, float target_amplitude)
{
    if (total_sweep_points < 5) {
        return start_freq;
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    float closest_freq = start_freq;
    float min_amplitude_diff = 1000.0f;

    // 在起始频率之上寻找最接近目标幅度的频率点
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > start_freq && sweep_results[i].adc2_amplitude > 0) {
            float amplitude_diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (amplitude_diff < min_amplitude_diff) {
                min_amplitude_diff = amplitude_diff;
                closest_freq = sweep_results[i].frequency;
            }
        }
    }

    return closest_freq;
}

/**
 * @brief  在指定频率之下寻找目标幅度对应的频率
 * @param  start_freq: 起始频率 (Hz)
 * @param  target_amplitude: 目标幅度值 (线性比值)
 * @retval 对应的频率 (Hz)，如果找不到返回start_freq
 * @note   用于寻找高通滤波器的衰减点
 */
float FindFreqBelow(float start_freq, float target_amplitude)
{
    if (total_sweep_points < 5) {
        return start_freq;
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;
    float closest_freq = start_freq;
    float min_amplitude_diff = 1000.0f;

    // 在起始频率之下寻找最接近目标幅度的频率点
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency < start_freq && sweep_results[i].frequency > 0 && sweep_results[i].adc2_amplitude > 0) {
            float amplitude_diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
            if (amplitude_diff < min_amplitude_diff) {
                min_amplitude_diff = amplitude_diff;
                closest_freq = sweep_results[i].frequency;
            }
        }
    }

    return closest_freq;
}

/**
 * @brief  计算低通滤波器的品质因数Q
 * @param  cutoff_freq: 截止频率 (Hz)
 * @retval 品质因数Q
 * @note   基于过渡带宽度计算Q值（-3dB到-20dB的频率差）
 */
float CalculateLowPassQ(float cutoff_freq)
{
    if (total_sweep_points < 10 || cutoff_freq <= 0) {
        printf("CalculateLowPassQ: Insufficient data or invalid cutoff frequency\r\n");
        return 0.707f; // 默认巴特沃斯响应
    }

    // 找到通带最大增益（使用全局变量max_voltage_ratio）
    float max_gain = max_voltage_ratio;
    if (max_gain <= 0) {
        printf("CalculateLowPassQ: Invalid max gain\r\n");
        return 0.707f;
    }

    // 计算-20dB的目标幅度值
    // -20dB = 20*log10(target/max) = -20
    // target/max = 10^(-20/20) = 10^(-1) = 0.1
    float target_20dB = max_gain * 0.1f;  // -20dB点

    // 在截止频率右侧寻找-20dB点
    float f_20dB = FindFreqAbove(cutoff_freq, target_20dB);

    // 检查是否找到有效的-20dB点
    if (f_20dB <= cutoff_freq || f_20dB > 400000.0f) {
        printf("CalculateLowPassQ: Cannot find valid -20dB point, f_20dB=%.1f\r\n", f_20dB);
        return 0.707f;
    }

    // 计算过渡带宽（从-3dB到-20dB）
    float transition_BW = f_20dB - cutoff_freq;

    // 经验公式：对于二阶低通滤波器
    // Q ≈ f₀ / (k × BW)，其中k约为1.8（经验值）
    float Q = cutoff_freq / (1.8f * transition_BW);

    // 限制Q值在合理范围内
    if (Q < 0.1f) Q = 0.1f;
    if (Q > 10.0f) Q = 10.0f;

    printf("LowPass Q calculation (transition bandwidth method):\r\n");
    printf("f0=%.1f Hz, f_20dB=%.1f Hz, transition_BW=%.1f Hz, Q=%.3f\r\n",
           cutoff_freq, f_20dB, transition_BW, Q);
    printf("max_gain=%.6f, target_20dB=%.6f\r\n", max_gain, target_20dB);

    return Q;
}

/**
 * @brief  计算高通滤波器的品质因数Q
 * @param  cutoff_freq: 截止频率 (Hz)
 * @retval 品质因数Q
 * @note   基于过渡带宽度计算Q值（-20dB到-3dB的频率差）
 */
float CalculateHighPassQ(float cutoff_freq)
{
    if (total_sweep_points < 10 || cutoff_freq <= 0) {
        printf("CalculateHighPassQ: Insufficient data or invalid cutoff frequency\r\n");
        return 0.707f; // 默认巴特沃斯响应
    }

    // 找到通带最大增益（使用全局变量max_voltage_ratio）
    float max_gain = max_voltage_ratio;
    if (max_gain <= 0) {
        printf("CalculateHighPassQ: Invalid max gain\r\n");
        return 0.707f;
    }

    // 计算-20dB的目标幅度值
    // -20dB = 20*log10(target/max) = -20
    // target/max = 10^(-20/20) = 10^(-1) = 0.1
    float target_20dB = max_gain * 0.1f;  // -20dB点

    // 在截止频率左侧寻找-20dB点
    float f_20dB = FindFreqBelow(cutoff_freq, target_20dB);

    // 检查是否找到有效的-20dB点
    if (f_20dB >= cutoff_freq || f_20dB < 1000.0f) {
        printf("CalculateHighPassQ: Cannot find valid -20dB point, f_20dB=%.1f\r\n", f_20dB);
        return 0.707f;
    }

    // 计算过渡带宽（从-20dB到-3dB）
    float transition_BW = cutoff_freq - f_20dB;

    // 经验公式：对于二阶高通滤波器
    // Q ≈ f₀ / (k × BW)，其中k约为1.8（经验值）
    float Q = cutoff_freq / (1.8f * transition_BW);

    // 限制Q值在合理范围内
    if (Q < 0.1f) Q = 0.1f;
    if (Q > 10.0f) Q = 10.0f;

    printf("HighPass Q calculation (transition bandwidth method):\r\n");
    printf("f0=%.1f Hz, f_20dB=%.1f Hz, transition_BW=%.1f Hz, Q=%.3f\r\n",
           cutoff_freq, f_20dB, transition_BW, Q);
    printf("max_gain=%.6f, target_20dB=%.6f\r\n", max_gain, target_20dB);

    return Q;
}

/**
 * @brief  配置MAX262滤波器以复现测量得到的滤波器特性
 * @param  filter_type: 滤波器类型字符串
 * @param  f0: 特征频率 (Hz) - 低通/高通为截止频率，带通/带阻为中心频率
 * @param  Q: 品质因数
 * @retval None
 * @note   根据滤波器类型选择合适的MAX262模式并配置参数
 *         - Low-Pass/High-Pass: f0 = 截止频率 (-3dB频率)
 *         - Band-Pass/Band-Stop: f0 = 中心频率 (谐振/陷波频率)
 */
void ConfigureMAX262Filter(const char* filter_type, float f0, float Q)
{
    printf("=== CONFIGURING MAX262 FILTER ===\r\n");
    printf("Target Filter Type: %s\r\n", filter_type);

    // 根据滤波器类型显示频率参数的含义
    if (strcmp(filter_type, "Low-Pass") == 0 || strcmp(filter_type, "High-Pass") == 0) {
        printf("Target Cutoff Frequency: %.1f Hz\r\n", f0);
    } else {
        printf("Target Center Frequency: %.1f Hz\r\n", f0);
    }

    printf("Target Q Factor: %.3f\r\n", Q);

    // 根据目标频率自动选择合适的时钟频率
    float current_clk = MAX262_GetClockFreq();
    float optimal_clk = current_clk;

    // 根据目标频率选择最佳时钟频率
    if (f0 < 1000.0f) {
        optimal_clk = 1000000.0f;  // 1MHz适合低频
    } else if (f0 < 10000.0f) {
        optimal_clk = 2000000.0f;  // 2MHz适合中低频
    } else if (f0 < 50000.0f) {
        optimal_clk = 4000000.0f;  // 4MHz适合中频
    } else {
        optimal_clk = 10000000.0f; // 10MHz适合高频
    }

    // 如果需要，更新时钟频率
    if (optimal_clk != current_clk) {
        printf("Adjusting MAX262 clock from %.0f Hz to %.0f Hz for better frequency coverage\r\n",
               current_clk, optimal_clk);

        // 使用MCO输出时钟到MAX262
        MAX262_MCO_SetFreq(optimal_clk);
    }

    // 获取当前模式下的频率范围
    float min_freq, max_freq;
    u8 test_mode = 1; // 先用模式1测试范围
    MAX262_GetFreqRange(test_mode, &min_freq, &max_freq);

    printf("Current MAX262 frequency range (Mode 1): %.1f Hz - %.1f Hz\r\n", min_freq, max_freq);

    // 检查频率是否在支持范围内
    if (f0 < min_freq || f0 > max_freq) {
        printf("Warning: Target frequency %.1f Hz is outside current range\r\n", f0);

        // 尝试调整到范围内
        if (f0 < min_freq) {
            f0 = min_freq;
            printf("Adjusted frequency to minimum: %.1f Hz\r\n", f0);
        } else if (f0 > max_freq) {
            f0 = max_freq;
            printf("Adjusted frequency to maximum: %.1f Hz\r\n", f0);
        }
    }

    // 限制Q值到MAX262支持的范围
    if (Q < 0.5f) {
        Q = 0.5f;
        printf("Q factor adjusted to minimum: %.3f\r\n", Q);
    } else if (Q > 50.0f) {
        Q = 50.0f;
        printf("Q factor adjusted to maximum: %.3f\r\n", Q);
    }

    // 根据滤波器类型选择MAX262模式并配置
    if (strcmp(filter_type, "Low-Pass") == 0) {
        // 低通滤波器 - 使用模式1
        printf("Configuring MAX262 as Low-Pass Filter (Mode 1)\r\n");
        Filter1(1, f0, Q);  // 模式1：低通
        Filter2(1, f0, Q);  // 两个滤波器都配置为相同参数

    } else if (strcmp(filter_type, "High-Pass") == 0) {
        // 高通滤波器 - 使用模式3
        printf("Configuring MAX262 as High-Pass Filter (Mode 3)\r\n");
        Filter1(3, f0, Q);  // 模式3：高通
        Filter2(3, f0, Q);  // 两个滤波器都配置为相同参数

    } else if (strcmp(filter_type, "Band-Pass") == 0) {
        // 带通滤波器 - 使用模式1（需要特殊配置）
        printf("Configuring MAX262 as Band-Pass Filter (Mode 1)\r\n");
        // 对于带通滤波器，可以使用两个滤波器级联
        // 或者使用模式1的特殊配置
        Filter1(1, f0, Q);  // 模式1可以配置为带通
        Filter2(1, f0, Q);

    } else if (strcmp(filter_type, "Band-Stop") == 0) {
        // 带阻滤波器 - 使用模式1（需要特殊配置）
        printf("Configuring MAX262 as Band-Stop Filter (Mode 1)\r\n");
        // 带阻滤波器需要特殊的配置方式
        Filter1(1, f0, Q);  // 模式1可以配置为带阻
        Filter2(1, f0, Q);

    } else {
        printf("Unknown filter type: %s, using default low-pass configuration\r\n", filter_type);
        Filter1(1, f0, Q);  // 默认使用低通模式
        Filter2(1, f0, Q);
    }

    printf("MAX262 configuration completed successfully!\r\n");
    printf("Filter1 and Filter2 configured with:\r\n");
    printf("  Frequency: %.1f Hz\r\n", f0);
    printf("  Q Factor: %.3f\r\n", Q);
    printf("=== END OF MAX262 CONFIGURATION ===\r\n");
}

/**
 * @brief  寻找低通滤波器的截止频率
 * @param  None
 * @retval 截止频率 (Hz)
 * @note   从通带最大值开始向高频寻找-3dB点（0.707倍最大值）
 */
float FindLowPassCutoff(void)
{
    if (total_sweep_points < 10) {
        printf("FindLowPassCutoff: Insufficient sweep data\r\n");
        return 10000.0f; // 默认值
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    // 寻找通带最大值（通常在低频段）
    float max_amplitude = 0.0f;
    float max_freq = 0.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].adc2_amplitude > max_amplitude) {
            max_amplitude = sweep_results[i].adc2_amplitude;
            max_freq = sweep_results[i].frequency;
        }
    }

    if (max_amplitude <= 0.0f) {
        printf("FindLowPassCutoff: No valid amplitude data found\r\n");
        return 10000.0f;
    }

    // 计算-3dB点的目标幅度（0.707倍最大值）
    float target_amplitude = max_amplitude * 0.707f;

    printf("Low-Pass analysis: max_amplitude=%.3f at %.1f Hz, target(-3dB)=%.3f\r\n",
           max_amplitude, max_freq, target_amplitude);

    // 从低频向高频寻找第一个小于等于目标幅度的点
    float cutoff_freq = 10000.0f;
    bool found = false;

    // 按频率排序寻找截止点
    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {
            // 寻找幅度下降到0.707倍的频率点
            if (sweep_results[i].adc2_amplitude <= target_amplitude) {
                cutoff_freq = sweep_results[i].frequency;
                found = true;
                printf("Found -3dB point: %.1f Hz, amplitude=%.3f\r\n",
                       cutoff_freq, sweep_results[i].adc2_amplitude);
                break;
            }
        }
    }

    if (!found) {
        printf("FindLowPassCutoff: No -3dB point found, using frequency with closest amplitude\r\n");
        // 寻找最接近目标幅度的频率点
        float min_diff = 1000.0f;
        for (int i = 0; i < valid_points; i++) {
            if (sweep_results[i].frequency > 0) {
                float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
                if (diff < min_diff) {
                    min_diff = diff;
                    cutoff_freq = sweep_results[i].frequency;
                }
            }
        }
    }

    return cutoff_freq;
}

/**
 * @brief  寻找高通滤波器的截止频率
 * @param  None
 * @retval 截止频率 (Hz)
 * @note   从通带最大值开始向低频寻找-3dB点（0.707倍最大值）
 */
float FindHighPassCutoff(void)
{
    if (total_sweep_points < 10) {
        printf("FindHighPassCutoff: Insufficient sweep data\r\n");
        return 5000.0f; // 默认值
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    // 寻找通带最大值（通常在高频段）
    float max_amplitude = 0.0f;
    float max_freq = 0.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0 && sweep_results[i].adc2_amplitude > max_amplitude) {
            max_amplitude = sweep_results[i].adc2_amplitude;
            max_freq = sweep_results[i].frequency;
        }
    }

    if (max_amplitude <= 0.0f) {
        printf("FindHighPassCutoff: No valid amplitude data found\r\n");
        return 5000.0f;
    }

    // 计算-3dB点的目标幅度（0.707倍最大值）
    float target_amplitude = max_amplitude * 0.707f;

    printf("High-Pass analysis: max_amplitude=%.3f at %.1f Hz, target(-3dB)=%.3f\r\n",
           max_amplitude, max_freq, target_amplitude);

    // 从高频向低频寻找第一个小于等于目标幅度的点
    float cutoff_freq = 5000.0f;
    bool found = false;

    // 按频率从高到低寻找截止点
    for (int i = valid_points - 1; i >= 0; i--) {
        if (sweep_results[i].frequency > 0) {
            // 寻找幅度下降到0.707倍的频率点
            if (sweep_results[i].adc2_amplitude <= target_amplitude) {
                cutoff_freq = sweep_results[i].frequency;
                found = true;
                printf("Found -3dB point: %.1f Hz, amplitude=%.3f\r\n",
                       cutoff_freq, sweep_results[i].adc2_amplitude);
                break;
            }
        }
    }

    if (!found) {
        printf("FindHighPassCutoff: No -3dB point found, using frequency with closest amplitude\r\n");
        // 寻找最接近目标幅度的频率点
        float min_diff = 1000.0f;
        for (int i = 0; i < valid_points; i++) {
            if (sweep_results[i].frequency > 0) {
                float diff = fabsf(sweep_results[i].adc2_amplitude - target_amplitude);
                if (diff < min_diff) {
                    min_diff = diff;
                    cutoff_freq = sweep_results[i].frequency;
                }
            }
        }
    }

    return cutoff_freq;
}