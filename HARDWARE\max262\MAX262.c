//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include <stm32f4xx.h>
#include "MAX262.h"
#include "delay.h"
#include <math.h>

// 定义时钟频率为1MHz（根据实际硬件配置修改）
#define CLK_FREQ 1000000.0f  // 1MHz，已修改为此值

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
u8 Fn(float f0, u8 mode);     // 根据中心频率和模式计算频率控制字


//-----------------------------------------------------------------
// u8 Qn(float q)                     
//-----------------------------------------------------------------
// 函数功能: 根据Q的值计算N
// 输入参数: Q
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
u8 Qn(float q, u8 mode)            // 品质因数转换函数，新增mode参数
{
    u8 temp;
    // 根据模式不同，Q值计算方式不同
    if (mode == 2)
        temp = 128 - (90.51f / q);  // 模式2的Q值计算公式
    else
        temp = 128 - (64.0f / q);   // 模式1,3,4的Q值计算公式
    return temp; 
}

//-----------------------------------------------------------------
// u8 Fn(float f0, u8 mode)                     
//-----------------------------------------------------------------
// 函数功能: 根据中心频率和模式计算频率控制字N
// 输入参数: 中心频率f0，工作模式mode
// 返 回 值: 频率控制字N
// 全局变量: CLK_FREQ
// 注意事项: 无
//-----------------------------------------------------------------
u8 Fn(float f0, u8 mode)            // 频率控制字计算函数
{
    float N_float;
    u8 N;
    
    if (mode == 2)
    {
        // 模式2: f0 = f_CLK / [(N + 26) × 1.11072]
        N_float = (CLK_FREQ / (f0 * 1.11072f)) - 26.0f;
    }
    else
    {
        // 模式1,3,4: f0 = f_CLK × 2 / [π × (26 + N)]
        N_float = (2.0f * CLK_FREQ) / (M_PI * f0) - 26.0f;
    }
    
    // 限制N的取值范围在0-127之间
    N = (u8)roundf(N_float);
    if (N < 0) N = 0;
    if (N > 127) N = 127;
    
    return N;
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float f0, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器1的模式、中心频率及Q值配置
// 输入参数: 模式 mode, 中心频率 f0, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter1(u8 mode, float f0, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq, fn;
	i = sq = fn = 0;
	
	fn = Fn(f0, mode);              // 计算频率控制字N
	sq = Qn(q, mode);               // 计算Q对应的控制字N，传入mode参数
	
	LE_H;                           // 使能锁存器
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 配置芯片模式 P15 Table4	
	GPIOB->BSRRH = 0x3f00;	          // 清除模式地址位
	delay_us(1);
	WR_L;                           // 写信号置低
	delay_us(1);
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    // 写入模式值到D1, D0
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 写入频率控制字
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+1)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(fn & a) << (8-2*i));	// 写入频率控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
	
	a = 0x03;
	
	// 写入Q值控制字
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+4)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));	// 写入Q值控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float f0, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器2的模式、中心频率及Q值配置
// 输入参数: 模式 mode, 中心频率 f0, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter2(u8 mode, float f0, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq, fn;
	i = sq = fn = 0;
	
	fn = Fn(f0, mode);              // 计算频率控制字N
	sq = Qn(q, mode);               // 计算Q对应的控制字N，传入mode参数
	
	LE_H;                           // 使能锁存器
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);

	GPIOB->BSRRH = 0x3f00;	          // 清除模式地址位
	GPIOB -> ODR |= (uint16_t)(8)<<10;          // 写入地址
	delay_us(1);
	WR_L;                           // 写信号置低
	delay_us(1);
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    // 写入模式值到D1, D0
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 写入频率控制字
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+9)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(fn & a) << (8-2*i));	// 写入频率控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
	
	a = 0x03;
	
	// 写入Q值控制字
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+12)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));	// 写入Q值控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
