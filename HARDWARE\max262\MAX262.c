//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include <stm32f4xx.h>
#include "MAX262.h"
#include "delay.h"
#include <math.h>
#include <stdio.h>

// MAX262时钟频率配置（可根据硬件配置修改）
static float CLK_FREQ = 2000000.0f;  // 默认2MHz，可动态修改

// 常用时钟频率预设
#define CLK_FREQ_1MHZ   1000000.0f
#define CLK_FREQ_2MHZ   2000000.0f
#define CLK_FREQ_4MHZ   4000000.0f
#define CLK_FREQ_10MHZ  10000000.0f

// 定义M_PI常数（如果math.h中没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
u8 Fn(float f0, u8 mode);     // 根据中心频率和模式计算频率控制字


//-----------------------------------------------------------------
// u8 Qn(float q)                     
//-----------------------------------------------------------------
// 函数功能: 根据Q的值计算N
// 输入参数: Q
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
u8 Qn(float q, u8 mode)            // 品质因数转换函数，新增mode参数
{
    u8 temp;
    // 根据模式不同，Q值计算方式不同
    if (mode == 2)
        temp = 128 - (90.51f / q);  // 模式2的Q值计算公式
    else
        temp = 128 - (64.0f / q);   // 模式1,3,4的Q值计算公式
    return temp; 
}

//-----------------------------------------------------------------
// u8 Fn(float f0, u8 mode)                     
//-----------------------------------------------------------------
// 函数功能: 根据中心频率和模式计算频率控制字N
// 输入参数: 中心频率f0，工作模式mode
// 返 回 值: 频率控制字N
// 全局变量: CLK_FREQ
// 注意事项: 无
//-----------------------------------------------------------------
u8 Fn(float f0, u8 mode)            // 频率控制字计算函数
{
    float N_float;
    u8 N;
    
    if (mode == 2)
    {
        // 模式2: f0 = f_CLK / [(N + 26) × 1.11072]
        N_float = (CLK_FREQ / (f0 * 1.11072f)) - 26.0f;
    }
    else
    {
        // 模式1,3,4: f0 = f_CLK × 2 / [π × (26 + N)]
        N_float = (2.0f * CLK_FREQ) / (M_PI * f0) - 26.0f;
    }
    
    // 限制N的取值范围在0-127之间
    if (N_float < 0.0f) N_float = 0.0f;
    if (N_float > 127.0f) N_float = 127.0f;
    N = (u8)roundf(N_float);
    
    return N;
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float f0, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器1的模式、中心频率及Q值配置
// 输入参数: 模式 mode, 中心频率 f0, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter1(u8 mode, float f0, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq, fn;
	i = sq = fn = 0;
	
	fn = Fn(f0, mode);              // 计算频率控制字N
	sq = Qn(q, mode);               // 计算Q对应的控制字N，传入mode参数
	
	LE_H;                           // 使能锁存器
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 配置芯片模式 P15 Table4	
	GPIOB->BSRRH = 0x3f00;	          // 清除模式地址位
	delay_us(1);
	WR_L;                           // 写信号置低
	delay_us(1);
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    // 写入模式值到D1, D0
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 写入频率控制字
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+1)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(fn & a) << (8-2*i));	// 写入频率控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
	
	a = 0x03;
	
	// 写入Q值控制字
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+4)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));	// 写入Q值控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float f0, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器2的模式、中心频率及Q值配置
// 输入参数: 模式 mode, 中心频率 f0, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter2(u8 mode, float f0, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq, fn;
	i = sq = fn = 0;
	
	fn = Fn(f0, mode);              // 计算频率控制字N
	sq = Qn(q, mode);               // 计算Q对应的控制字N，传入mode参数
	
	LE_H;                           // 使能锁存器
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);

	GPIOB->BSRRH = 0x3f00;	          // 清除模式地址位
	GPIOB -> ODR |= (uint16_t)(8)<<10;          // 写入地址
	delay_us(1);
	WR_L;                           // 写信号置低
	delay_us(1);
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    // 写入模式值到D1, D0
	delay_us(1);
	WR_H;                           // 写信号置高
	delay_us(1);
	
	// 写入频率控制字
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+9)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(fn & a) << (8-2*i));	// 写入频率控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
	
	a = 0x03;
	
	// 写入Q值控制字
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BSRRH = 0x3f00;	          // 清除地址位
		GPIOB -> ODR |= (uint16_t)(i+12)<<10;          // 写入地址
		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));	// 写入Q值控制字
		delay_us(1);
		WR_H;                             // 写信号置高
		a = a << 2;                       // a左移2位		
	}
}

//-----------------------------------------------------------------
// void MAX262_SetClockFreq(float freq_hz)
//-----------------------------------------------------------------
// 函数功能: 设置MAX262的时钟频率
// 输入参数: freq_hz - 时钟频率(Hz)
// 返 回 值: 无
// 全局变量: CLK_FREQ
// 注意事项: 改变时钟频率会影响所有滤波器的频率范围
//-----------------------------------------------------------------
void MAX262_SetClockFreq(float freq_hz)
{
    if (freq_hz > 0.0f && freq_hz <= 50000000.0f) {  // 限制在合理范围内
        CLK_FREQ = freq_hz;
    }
}

//-----------------------------------------------------------------
// float MAX262_GetClockFreq(void)
//-----------------------------------------------------------------
// 函数功能: 获取当前MAX262的时钟频率
// 输入参数: 无
// 返 回 值: 当前时钟频率(Hz)
// 全局变量: CLK_FREQ
// 注意事项: 无
//-----------------------------------------------------------------
float MAX262_GetClockFreq(void)
{
    return CLK_FREQ;
}

//-----------------------------------------------------------------
// void MAX262_GetFreqRange(u8 mode, float* min_freq, float* max_freq)
//-----------------------------------------------------------------
// 函数功能: 获取指定模式下的频率范围
// 输入参数: mode - 滤波器模式(1,2,3,4)
// 输出参数: min_freq - 最小频率指针, max_freq - 最大频率指针
// 返 回 值: 无
// 全局变量: CLK_FREQ
// 注意事项: 基于当前时钟频率计算
//-----------------------------------------------------------------
void MAX262_GetFreqRange(u8 mode, float* min_freq, float* max_freq)
{
    if (mode == 2) {
        // 模式2: f0 = CLK_FREQ / [(N + 26) × 1.11072]
        *max_freq = CLK_FREQ / (26.0f * 1.11072f);      // N=0时的最大频率
        *min_freq = CLK_FREQ / (153.0f * 1.11072f);     // N=127时的最小频率
    } else {
        // 模式1,3,4: f0 = CLK_FREQ × 2 / [π × (26 + N)]
        *max_freq = (2.0f * CLK_FREQ) / (M_PI * 26.0f); // N=0时的最大频率
        *min_freq = (2.0f * CLK_FREQ) / (M_PI * 153.0f);// N=127时的最小频率
    }
}

//-----------------------------------------------------------------
// void MAX262_MCO_Init(void)
//-----------------------------------------------------------------
// 函数功能: 初始化MCO1引脚(PA8)用于输出时钟给MAX262
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 使用PA8引脚输出时钟，需要连接到MAX262的CLK引脚
//-----------------------------------------------------------------
void MAX262_MCO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOA时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);

    // 配置PA8为MCO1输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置PA8的复用功能为MCO1
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource8, GPIO_AF_MCO);

    // 默认输出2MHz时钟 (HSE 8MHz / 4 = 2MHz)
    RCC_MCO1Config(RCC_MCO1Source_HSE, RCC_MCO1Div_4);

    printf("MCO1 initialized on PA8, outputting 2MHz clock for MAX262\r\n");
}

//-----------------------------------------------------------------
// void MAX262_MCO_SetFreq(float target_freq)
//-----------------------------------------------------------------
// 函数功能: 设置MCO1输出频率
// 输入参数: target_freq - 目标频率(Hz)
// 返 回 值: 无
// 全局变量: CLK_FREQ
// 注意事项: 根据目标频率选择合适的时钟源和分频比
//-----------------------------------------------------------------
void MAX262_MCO_SetFreq(float target_freq)
{
    uint32_t mco_source;
    uint32_t mco_div;
    float actual_freq;

    printf("Setting MCO1 frequency to %.0f Hz...\r\n", target_freq);

    // 根据目标频率选择时钟源和分频比
    // HSE = 8MHz, HSI = 16MHz, PLLCLK = 168MHz
    // 注意：此库版本只支持1-5分频

    if (target_freq <= 1600000.0f) {
        // 1.6MHz及以下：使用HSE 8MHz
        mco_source = RCC_MCO1Source_HSE;
        mco_div = RCC_MCO1Div_5;  // 8MHz/5 = 1.6MHz
        actual_freq = 1600000.0f;
    } else if (target_freq <= 2000000.0f) {
        // 1.6MHz - 2MHz：使用HSE
        mco_source = RCC_MCO1Source_HSE;
        mco_div = RCC_MCO1Div_4;  // 8MHz/4 = 2MHz
        actual_freq = 2000000.0f;
    } else if (target_freq <= 2700000.0f) {
        // 2MHz - 2.7MHz：使用HSE
        mco_source = RCC_MCO1Source_HSE;
        mco_div = RCC_MCO1Div_3;  // 8MHz/3 = 2.67MHz
        actual_freq = 2666667.0f;
    } else if (target_freq <= 4000000.0f) {
        // 2.7MHz - 4MHz：使用HSE
        mco_source = RCC_MCO1Source_HSE;
        mco_div = RCC_MCO1Div_2;  // 8MHz/2 = 4MHz
        actual_freq = 4000000.0f;
    } else if (target_freq <= 8000000.0f) {
        // 4MHz - 8MHz：使用HSE
        mco_source = RCC_MCO1Source_HSE;
        mco_div = RCC_MCO1Div_1;  // 8MHz/1 = 8MHz
        actual_freq = 8000000.0f;
    } else if (target_freq <= 16000000.0f) {
        // 8MHz - 16MHz：使用HSI
        mco_source = RCC_MCO1Source_HSI;
        mco_div = RCC_MCO1Div_1;  // 16MHz/1 = 16MHz
        actual_freq = 16000000.0f;
    } else if (target_freq <= 33600000.0f) {
        // 16MHz - 33.6MHz：使用PLLCLK
        mco_source = RCC_MCO1Source_PLLCLK;
        mco_div = RCC_MCO1Div_5;  // 168MHz/5 = 33.6MHz
        actual_freq = 33600000.0f;
    } else if (target_freq <= 42000000.0f) {
        // 33.6MHz - 42MHz：使用PLLCLK
        mco_source = RCC_MCO1Source_PLLCLK;
        mco_div = RCC_MCO1Div_4;  // 168MHz/4 = 42MHz
        actual_freq = 42000000.0f;
    } else if (target_freq <= 56000000.0f) {
        // 42MHz - 56MHz：使用PLLCLK
        mco_source = RCC_MCO1Source_PLLCLK;
        mco_div = RCC_MCO1Div_3;  // 168MHz/3 = 56MHz
        actual_freq = 56000000.0f;
    } else if (target_freq <= 84000000.0f) {
        // 56MHz - 84MHz：使用PLLCLK
        mco_source = RCC_MCO1Source_PLLCLK;
        mco_div = RCC_MCO1Div_2;  // 168MHz/2 = 84MHz
        actual_freq = 84000000.0f;
    } else {
        // 84MHz以上：使用PLLCLK最高频率
        mco_source = RCC_MCO1Source_PLLCLK;
        mco_div = RCC_MCO1Div_1;  // 168MHz/1 = 168MHz
        actual_freq = 168000000.0f;
    }

    // 配置MCO1输出
    RCC_MCO1Config(mco_source, mco_div);

    // 更新MAX262的时钟频率设置
    CLK_FREQ = actual_freq;

    printf("MCO1 configured: actual frequency = %.0f Hz\r\n", actual_freq);
    printf("MAX262 clock frequency updated to %.0f Hz\r\n", CLK_FREQ);
}

//-----------------------------------------------------------------
// void MAX262_MCO_Disable(void)
//-----------------------------------------------------------------
// 函数功能: 禁用MCO1时钟输出
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 禁用后PA8引脚可用作普通GPIO
//-----------------------------------------------------------------
void MAX262_MCO_Disable(void)
{
    // 禁用MCO1输出 - 通过配置为HSI最低频率来"禁用"
    RCC_MCO1Config(RCC_MCO1Source_HSI, RCC_MCO1Div_5);  // 16MHz/5 = 3.2MHz (最低可用频率)

    // 将PA8配置为普通GPIO输入，断开MCO功能
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    printf("MCO1 clock output disabled\r\n");
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
